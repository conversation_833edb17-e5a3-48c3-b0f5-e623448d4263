class LunarDescentGuidance:
    def __init__(self):
        # Initialize guidance parameters
        self.program_number = 0
        self.wch_vert = 0
        self.wch_phase = 0
        self.x_override = False
        
        # Lunar gravity (m/s^2)
        self.lunar_gravity = 1.62
        self.g_eff = np.array([0.0, 0.0, self.lunar_gravity])
        
        # Guidance parameters
        self.tau_vert = 4.0  # Time constant for vertical control
        
        # State vectors
        self.r_gu = np.zeros(3)  # Position vector [x, y, z]
        self.v_gu = np.zeros(3)  # Velocity vector [vx, vy, vz]
        self.v2fg = np.zeros(3)  # Desired velocity vector
        
        # Display variables
        self.altitude = 0.0
        self.alt_rate = 0.0
        self.hor_velocity = 0.0
    
    def p65_start(self):
        """Initialize P65 vertical descent guidance"""
        self.program_number = 65
        self.wch_vert = -2  # P65 mode
        self.x_override = True
        self.wch_phase = 2
        print("P65 Vertical Descent Guidance Started")
        
        # Set default descent rate (1.22 m/s)
        self.set_descent_rate(1.22)
    
    def update_state(self, position, velocity):
        """Update guidance state with current position and velocity"""
        self.r_gu = np.array(position)
        self.v_gu = np.array(velocity)
        
        # Update display variables
        self.altitude = -self.r_gu[2]  # Z is negative altitude
        self.alt_rate = -self.v_gu[2]  # Z velocity is negative of descent rate
        self.hor_velocity = np.sqrt(self.v_gu[0]**2 + self.v_gu[1]**2)
    
    def set_descent_rate(self, rate):
        """Set the desired vertical descent rate (positive down)"""
        # v2fg is desired velocity vector, z-component is negative of descent rate
        self.v2fg[2] = -rate
    
    def p65_guidance(self):
        """
        P65 vertical descent guidance algorithm
        
        The P65 guidance equation is:
        ACG = (V2FG - VGU) / TAUVERT - G_EFF
        
        Returns acceleration command vector [ax, ay, az]
        """
        # Calculate velocity error
        velocity_error = self.v2fg - self.v_gu
        
        # Apply P65 guidance equation
        accel_command = velocity_error / self.tau_vert - self.g_eff
        
        return accel_command
    
    def display_status(self):
        """Display current guidance status"""
        print(f"P{self.program_number} Status:")
        print(f"  Altitude: {self.altitude:.1f} m")
        print(f"  Descent Rate: {self.alt_rate:.2f} m/s")
        print(f"  Horizontal Velocity: {self.hor_velocity:.2f} m/s")
        print(f"  Position: [{self.r_gu[0]:.1f}, {self.r_gu[1]:.1f}, {self.r_gu[2]:.1f}] m")
        print(f"  Velocity: [{self.v_gu[0]:.2f}, {self.v_gu[1]:.2f}, {self.v_gu[2]:.2f}] m/s")